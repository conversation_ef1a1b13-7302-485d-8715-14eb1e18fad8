<!DOCTYPE html>
<html xmlns:th="http://www.thymeleaf.org"
      xmlns:layout="http://www.ultraq.net.nz/thymeleaf/layout"
      layout:decorate="~{admin/layout}">
<head>
    <title>Qu<PERSON><PERSON> lý sản phẩm</title>
    <link rel="stylesheet" href="https://cdn.datatables.net/1.13.4/css/jquery.dataTables.min.css" />
</head>

<body>
<section layout:fragment="content">
    <div>
        <h1 class="mb-4">Quản lý sản phẩm</h1>
        <a href="/admin/san-pham/them" class="btn btn-primary mb-3">Thêm sản phẩm</a>
        <form th:action="@{/admin/san-pham/ap-dung-giam-gia-nhieu}" method="post">
            <div class="row mb-3">
                <div class="col-md-3">
                    <select name="dotGiamGiaId" class="form-select" required>
                        <option value="">-- Chọn đợt giảm giá --</option>
                        <option th:each="dgg : ${dsDotGiamGia}" th:value="${dgg.id}" th:text="${dgg.ten}"></option>
                    </select>
                </div>
                <div class="col-md-3">
                    <button type="submit" class="btn btn-success">Áp dụng giảm giá</button>
                </div>
            </div>
        </form>
        <div th:if="${error}" class="alert alert-danger alert-dismissible fade show mt-3 mx-3" role="alert">
            <i class="bi bi-exclamation-triangle-fill"></i>
            <span th:text="${error}"></span>
            <button type="button" class="btn-close" data-bs-dismiss="alert" aria-label="Close"></button>
        </div>

        <!-- Thông báo thành công -->
        <div th:if="${success}" class="alert alert-success alert-dismissible fade show mt-3 mx-3" role="alert">
            <i class="bi bi-check-circle-fill"></i>
            <span th:text="${success}"></span>
            <button type="button" class="btn-close" data-bs-dismiss="alert" aria-label="Close"></button>
        </div>
        <table id="productTable" class="table table-bordered table-striped">
            <thead>
            <tr>
                <th>ID</th>
                <th>Mã</th>
                <th>Tên</th>
                <th>Mô tả</th>
                <th>Ngày tạo</th>
                <th>Ngày sửa</th>
                <th>Trạng thái</th>
                <th>Chất liệu</th>
                <th>Danh mục</th>
                <th>Thương hiệu</th>
                <th>Xuất xứ</th>
                <th>Kiểu dáng</th>
                <th>Loại thú</th>
                <th>Thao tác</th>
            </tr>
            </thead>
            <tbody>
            <tr th:each="sp : ${list}">
                <td th:text="${sp.id}"></td>
                <td th:text="${sp.ma}"></td>
                <td th:text="${sp.ten}"></td>
                <td th:text="${sp.moTa}"></td>
                <td th:text="${sp.ngayTao}"></td>
                <td th:text="${sp.ngaySua}"></td>
                <td th:text="${sp.trangThai ? 'Đang hoạt động' : 'Ngừng bán'}"></td>
                <td th:text="${sp.chatLieu.ten}"></td>
                <td th:text="${sp.danhMuc.ten}"></td>
                <td th:text="${sp.thuongHieu.ten}"></td>
                <td th:text="${sp.xuatXu.ten}"></td>
                <td th:text="${sp.kieuDang.ten}"></td>
                <td th:text="${sp.loaiThu.ten}"></td>
                <td>
                    <a th:href="@{'/admin/san-pham/sua/' + ${sp.id}}" class="btn btn-sm btn-warning">Sửa</a>
                    <a th:href="@{'/admin/san-pham/xem/' + ${sp.id}}" class="btn btn-sm btn-info">Xem</a>
                    <a th:href="@{'/admin/san-pham/xoa/' + ${sp.id}}" class="btn btn-sm btn-danger"
                       onclick="return confirm('Bạn có chắc muốn xoá sản phẩm này?')">Xoá</a>
                </td>
            </tr>
            </tbody>
        </table>
    </div>

    <script src="https://code.jquery.com/jquery-3.6.0.min.js"></script>
    <script src="https://cdn.datatables.net/1.13.4/js/jquery.dataTables.min.js"></script>
    <script>
        $(document).ready(function () {
            $('#productTable').DataTable();
        });
    </script>
</section>
</body>
</html>


<div class="w-full flex justify-between items-center mb-3 mt-1 pl-3">
    <div>
        <h3 class="text-lg font-semibold text-slate-800">Shopping Cart</h3>
        <p class="text-slate-500">Review your selected items.</p>
    </div>
    <div class="mx-3">
        <div class="w-full max-w-sm min-w-[200px] relative">
        <div class="relative">
            <input
            class="bg-white w-full pr-11 h-10 pl-3 py-2 bg-transparent placeholder:text-slate-400 text-slate-700 text-sm border border-slate-200 rounded transition duration-300 ease focus:outline-none focus:border-slate-400 hover:border-slate-400 shadow-sm focus:shadow-md"
            placeholder="Search for product..."
            />
            <button
            class="absolute h-8 w-8 right-1 top-1 my-auto px-2 flex items-center bg-white rounded "
            type="button"
            >
            <svg xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24" stroke-width="3" stroke="currentColor" class="w-8 h-8 text-slate-600">
                <path stroke-linecap="round" stroke-linejoin="round" d="m21 21-5.197-5.197m0 0A7.5 7.5 0 1 0 5.196 5.196a7.5 7.5 0 0 0 10.607 10.607Z" />
            </svg>
            </button>
        </div>
        </div>
    </div>
</div>
 
<div class="relative flex flex-col w-full h-full overflow-scroll text-gray-700 bg-white shadow-md rounded-lg bg-clip-border">
  <table class="w-full text-left table-auto min-w-max">
    <thead>
      <tr class="border-b border-slate-300 bg-slate-50">
        <th class="p-4 text-sm font-normal leading-none text-slate-500">Product</th>
        <th class="p-4 text-sm font-normal leading-none text-slate-500">Name</th>
        <th class="p-4 text-sm font-normal leading-none text-slate-500">Quantity</th>
        <th class="p-4 text-sm font-normal leading-none text-slate-500">Price per Item</th>
        <th class="p-4 text-sm font-normal leading-none text-slate-500">Total Price</th>
        <th class="p-4 text-sm font-normal leading-none text-slate-500"></th>
      </tr>
    </thead>
    <tbody>
      <tr class="hover:bg-slate-50">
        <td class="p-4 border-b border-slate-200 py-5">
          <img src="https://demos.creative-tim.com/corporate-ui-dashboard-pro/assets/img/kam-idris-_HqHX3LBN18-unsplash.jpg" alt="Product 1" class="w-16 h-16 object-cover rounded" />
        </td>
        <td class="p-4 border-b border-slate-200 py-5">
          <p class="block font-semibold text-sm text-slate-800">Beautiful Chair</p>
        </td>
        <td class="p-4 border-b border-slate-200 py-5">
          <p class="text-sm text-slate-500">2</p>
        </td>
        <td class="p-4 border-b border-slate-200 py-5">
          <p class="text-sm text-slate-500">$500</p>
        </td>
        <td class="p-4 border-b border-slate-200 py-5">
          <p class="text-sm text-slate-500">$1,000</p>
        </td>
        <td class="p-4 border-b border-slate-200 py-5">
          <button type="button" class="text-slate-500 hover:text-slate-700">
            <svg xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24" stroke-width="2" stroke="currentColor" class="w-6 h-6">
              <path stroke-linecap="round" stroke-linejoin="round" d="M6 18L18 6M6 6l12 12" />
            </svg>
          </button>
        </td>
      </tr>
      <tr class="hover:bg-slate-50">
        <td class="p-4 border-b border-slate-200 py-5">
          <img src="https://demos.creative-tim.com/corporate-ui-dashboard-pro/assets/img/spacejoy-NpF_OYE301E-unsplash.jpg" alt="Product 2" class="w-16 h-16 object-cover rounded" />
        </td>
        <td class="p-4 border-b border-slate-200 py-5">
          <p class="block font-semibold text-sm text-slate-800">Little Sofa</p>
        </td>
        <td class="p-4 border-b border-slate-200 py-5">
          <p class="text-sm text-slate-500">1</p>
        </td>
        <td class="p-4 border-b border-slate-200 py-5">
          <p class="text-sm text-slate-500">$750</p>
        </td>
        <td class="p-4 border-b border-slate-200 py-5">
          <p class="text-sm text-slate-500">$750</p>
        </td>
        <td class="p-4 border-b border-slate-200 py-5">
          <button type="button" class="text-slate-500 hover:text-slate-700">
            <svg xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24" stroke-width="2" stroke="currentColor" class="w-6 h-6">
              <path stroke-linecap="round" stroke-linejoin="round" d="M6 18L18 6M6 6l12 12" />
            </svg>
          </button>
        </td>
      </tr>
      <tr class="hover:bg-slate-50">
        <td class="p-4 border-b border-slate-200 py-5">
          <img src="https://demos.creative-tim.com/corporate-ui-dashboard-pro/assets/img/michael-oxendine-GHCVUtBECuY-unsplash.jpg" alt="Product 3" class="w-16 h-16 object-cover rounded" />
        </td>
        <td class="p-4 border-b border-slate-200 py-5">
          <p class="block font-semibold text-sm text-slate-800">Brown Coach</p>
        </td>
        <td class="p-4 border-b border-slate-200 py-5">
          <p class="text-sm text-slate-500">3</p>
        </td>
        <td class="p-4 border-b border-slate-200 py-5">
          <p class="text-sm text-slate-500">$3,000</p>
        </td>
        <td class="p-4 border-b border-slate-200 py-5">
          <p class="text-sm text-slate-500">$9,000</p>
        </td>
        <td class="p-4 border-b border-slate-200 py-5">
          <button type="button" class="text-slate-500 hover:text-slate-700">
            <svg xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24" stroke-width="2" stroke="currentColor" class="w-6 h-6">
              <path stroke-linecap="round" stroke-linejoin="round" d="M6 18L18 6M6 6l12 12" />
            </svg>
          </button>
        </td>
      </tr>
    </tbody>
  </table>
</div>
