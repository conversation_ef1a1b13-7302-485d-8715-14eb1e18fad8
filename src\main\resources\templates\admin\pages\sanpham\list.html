<!DOCTYPE html>
<html xmlns:th="http://www.thymeleaf.org"
      xmlns:layout="http://www.ultraq.net.nz/thymeleaf/layout"
      layout:decorate="~{admin/layout}">
<head>
    <title><PERSON>u<PERSON><PERSON> lý sản phẩm</title>
    <script src="https://cdn.tailwindcss.com"></script>
</head>

<body>
<section layout:fragment="content">
    <div class="p-6">
        <!-- Header Section -->
        <div class="w-full flex justify-between items-center mb-3 mt-1 pl-3">
            <div>
                <h3 class="text-lg font-semibold text-slate-800">Quản lý sản phẩm</h3>
                <p class="text-slate-500">Danh sách tất cả sản phẩm trong hệ thống.</p>
            </div>
            <div class="mx-3">
                <div class="w-full max-w-sm min-w-[200px] relative">
                    <div class="relative">
                        <input
                        class="bg-white w-full pr-11 h-10 pl-3 py-2 bg-transparent placeholder:text-slate-400 text-slate-700 text-sm border border-slate-200 rounded transition duration-300 ease focus:outline-none focus:border-slate-400 hover:border-slate-400 shadow-sm focus:shadow-md"
                        placeholder="Tìm kiếm sản phẩm..."
                        id="searchInput"
                        />
                        <button
                        class="absolute h-8 w-8 right-1 top-1 my-auto px-2 flex items-center bg-white rounded"
                        type="button"
                        >
                        <svg xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24" stroke-width="3" stroke="currentColor" class="w-8 h-8 text-slate-600">
                            <path stroke-linecap="round" stroke-linejoin="round" d="m21 21-5.197-5.197m0 0A7.5 7.5 0 1 0 5.196 5.196a7.5 7.5 0 0 0 10.607 10.607Z" />
                        </svg>
                        </button>
                    </div>
                </div>
            </div>
        </div>

        <!-- Action Buttons -->
        <div class="mb-4 flex flex-wrap gap-3">
            <a href="/admin/san-pham/them" class="bg-blue-600 hover:bg-blue-700 text-white px-4 py-2 rounded-lg text-sm font-medium transition duration-200">
                Thêm sản phẩm
            </a>
            <form th:action="@{/admin/san-pham/ap-dung-giam-gia-nhieu}" method="post" class="flex gap-3 items-center">
                <select name="dotGiamGiaId" class="border border-slate-300 rounded-lg px-3 py-2 text-sm focus:outline-none focus:border-slate-400" required>
                    <option value="">-- Chọn đợt giảm giá --</option>
                    <option th:each="dgg : ${dsDotGiamGia}" th:value="${dgg.id}" th:text="${dgg.ten}"></option>
                </select>
                <button type="submit" class="bg-green-600 hover:bg-green-700 text-white px-4 py-2 rounded-lg text-sm font-medium transition duration-200">
                    Áp dụng giảm giá
                </button>
            </form>
        </div>

        <!-- Alert Messages -->
        <div th:if="${error}" class="bg-red-50 border border-red-200 text-red-700 px-4 py-3 rounded-lg mb-4" role="alert">
            <div class="flex items-center">
                <svg class="w-5 h-5 mr-2" fill="currentColor" viewBox="0 0 20 20">
                    <path fill-rule="evenodd" d="M18 10a8 8 0 11-16 0 8 8 0 0116 0zm-7-4a1 1 0 11-2 0 1 1 0 012 0zM9 9a1 1 0 000 2v3a1 1 0 001 1h1a1 1 0 100-2v-3a1 1 0 00-1-1H9z" clip-rule="evenodd"></path>
                </svg>
                <span th:text="${error}"></span>
            </div>
        </div>

        <div th:if="${success}" class="bg-green-50 border border-green-200 text-green-700 px-4 py-3 rounded-lg mb-4" role="alert">
            <div class="flex items-center">
                <svg class="w-5 h-5 mr-2" fill="currentColor" viewBox="0 0 20 20">
                    <path fill-rule="evenodd" d="M10 18a8 8 0 100-16 8 8 0 000 16zm3.707-9.293a1 1 0 00-1.414-1.414L9 10.586 7.707 9.293a1 1 0 00-1.414 1.414l2 2a1 1 0 001.414 0l4-4z" clip-rule="evenodd"></path>
                </svg>
                <span th:text="${success}"></span>
            </div>
        </div>
        <!-- Product Table -->
        <div class="relative flex flex-col w-full h-full text-gray-700 bg-white shadow-md rounded-lg bg-clip-border">
            <table class="w-full text-left table-auto" id="productTable">
                <thead>
                    <tr class="border-b border-slate-300 bg-slate-50">
                        <th class="p-3 text-sm font-normal leading-none text-slate-500 w-20">Ảnh</th>
                        <th class="p-3 text-sm font-normal leading-none text-slate-500 w-32">Mã & Tên SP</th>
                        <th class="p-3 text-sm font-normal leading-none text-slate-500 w-40">Thông tin sản phẩm</th>
                        <th class="p-3 text-sm font-normal leading-none text-slate-500 w-40">Thông tin khác</th>
                        <th class="p-3 text-sm font-normal leading-none text-slate-500 w-24">Ngày tạo</th>
                        <th class="p-3 text-sm font-normal leading-none text-slate-500 w-32">Thao tác</th>
                    </tr>
                </thead>
                <tbody>
                    <tr th:each="sp : ${list}" class="hover:bg-slate-50 transition-colors">
                        <!-- Hình ảnh -->
                        <td class="p-3 border-b border-slate-200">
                            <img th:if="${sp.hinhAnhs != null and !sp.hinhAnhs.empty}"
                                 th:src="${sp.hinhAnhs.iterator().next().url}"
                                 th:alt="${sp.ten}"
                                 class="w-12 h-12 object-cover rounded-lg shadow-sm" />
                            <div th:unless="${sp.hinhAnhs != null and !sp.hinhAnhs.empty}"
                                 class="w-12 h-12 bg-gray-100 rounded-lg flex items-center justify-center">
                                <svg class="w-6 h-6 text-gray-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M4 16l4.586-4.586a2 2 0 012.828 0L16 16m-2-2l1.586-1.586a2 2 0 012.828 0L20 14m-6-6h.01M6 20h12a2 2 0 002-2V6a2 2 0 00-2-2H6a2 2 0 00-2 2v12a2 2 0 002 2z"></path>
                                </svg>
                            </div>
                        </td>

                        <!-- Mã & Tên sản phẩm -->
                        <td class="p-3 border-b border-slate-200">
                            <div class="space-y-1">
                                <p class="font-semibold text-sm text-slate-800" th:text="${sp.ma}"></p>
                                <p class="text-xs text-slate-600 font-medium" th:text="${sp.ten}"></p>
                                <p th:if="${sp.moTa != null and !sp.moTa.isEmpty()}"
                                   class="text-xs text-slate-400 truncate max-w-32"
                                   th:text="${sp.moTa}"
                                   th:title="${sp.moTa}"></p>
                            </div>
                        </td>

                        <!-- Thông tin sản phẩm (Danh mục, Loại thú, Chất liệu) -->
                        <td class="p-3 border-b border-slate-200">
                            <div class="space-y-2">
                                <div class="flex flex-wrap gap-1">
                                    <span class="inline-flex items-center px-2 py-1 rounded-full text-xs font-medium bg-blue-100 text-blue-800" th:text="${sp.danhMuc.ten}"></span>
                                    <span class="inline-flex items-center px-2 py-1 rounded-full text-xs font-medium bg-green-100 text-green-800" th:text="${sp.loaiThu.ten}"></span>
                                </div>
                                <div class="text-xs text-slate-500">
                                    <span class="font-medium">Chất liệu:</span> <span th:text="${sp.chatLieu.ten}"></span>
                                </div>
                            </div>
                        </td>

                        <!-- Thông tin khác (Thương hiệu, Xuất xứ, Kiểu dáng) -->
                        <td class="p-3 border-b border-slate-200">
                            <div class="space-y-1 text-xs text-slate-500">
                                <div><span class="font-medium">TH:</span> <span th:text="${sp.thuongHieu.ten}"></span></div>
                                <div><span class="font-medium">XX:</span> <span th:text="${sp.xuatXu.ten}"></span></div>
                                <div><span class="font-medium">KD:</span> <span th:text="${sp.kieuDang.ten}"></span></div>
                            </div>
                        </td>

                        <!-- Ngày tạo -->
                        <td class="p-3 border-b border-slate-200">
                            <p class="text-xs text-slate-500" th:text="${#temporals.format(sp.ngayTao, 'dd/MM/yy')}"></p>
                        </td>

                        <!-- Thao tác -->
                        <td class="p-3 border-b border-slate-200">
                            <div class="flex flex-col gap-1">
                                <a th:href="@{'/admin/san-pham/xem/' + ${sp.id}}"
                                   class="inline-flex items-center justify-center px-2 py-1 text-xs font-medium text-blue-600 bg-blue-50 rounded hover:bg-blue-100 transition-colors">
                                    Xem
                                </a>
                                <a th:href="@{'/admin/san-pham/sua/' + ${sp.id}}"
                                   class="inline-flex items-center justify-center px-2 py-1 text-xs font-medium text-yellow-600 bg-yellow-50 rounded hover:bg-yellow-100 transition-colors">
                                    Sửa
                                </a>
                                <a th:href="@{'/admin/san-pham/xoa/' + ${sp.id}}"
                                   class="inline-flex items-center justify-center px-2 py-1 text-xs font-medium text-red-600 bg-red-50 rounded hover:bg-red-100 transition-colors"
                                   onclick="return confirm('Bạn có chắc muốn xoá sản phẩm này?')">
                                    Xoá
                                </a>
                            </div>
                        </td>
                    </tr>
                </tbody>
            </table>
        </div>
    </div>

    <script>
        // Search functionality
        document.getElementById('searchInput').addEventListener('input', function() {
            const searchTerm = this.value.toLowerCase();
            const tableRows = document.querySelectorAll('#productTable tbody tr');

            tableRows.forEach(row => {
                const cells = row.querySelectorAll('td');
                let found = false;

                // Search in product code, name, description, category, brand, etc.
                for (let i = 1; i < cells.length - 1; i++) { // Skip image column (0) and action column (last)
                    const cellText = cells[i].textContent.toLowerCase();
                    if (cellText.includes(searchTerm)) {
                        found = true;
                        break;
                    }
                }

                row.style.display = found ? '' : 'none';
            });
        });

        // Add some hover effects and animations
        document.addEventListener('DOMContentLoaded', function() {
            const rows = document.querySelectorAll('#productTable tbody tr');
            rows.forEach(row => {
                row.addEventListener('mouseenter', function() {
                    this.style.transform = 'scale(1.01)';
                    this.style.transition = 'transform 0.2s ease';
                });

                row.addEventListener('mouseleave', function() {
                    this.style.transform = 'scale(1)';
                });
            });
        });
    </script>
</section>
</body>
</html>