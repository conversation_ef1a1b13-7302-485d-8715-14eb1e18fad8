<!DOCTYPE html>
<html xmlns:th="http://www.thymeleaf.org"
      xmlns:layout="http://www.ultraq.net.nz/thymeleaf/layout"
      layout:decorate="~{admin/layout}">
<head>
    <title>Qu<PERSON>n lý sản phẩm</title>
    <script src="https://cdn.tailwindcss.com"></script>
    <script>
        // Configure Tailwind to avoid conflicts with Bootstrap
        tailwind.config = {
            corePlugins: {
                preflight: false, // Disable Tailwind's CSS reset
            },
            prefix: 'tw-', // Add prefix to all Tailwind classes
        }
    </script>
    <style>
        /* Ensure Bootstrap sidebar styles are preserved */
        .sidebar .nav .nav-item .collapse.show {
            display: block !important;
        }

        .sidebar .nav .nav-item.active > .nav-link {
            color: #dead6f !important;
        }

        .sidebar .nav.sub-menu .nav-item .nav-link.active {
            color: #dead6f !important;
        }
    </style>
</head>

<body>
<section layout:fragment="content">
    <div class="tw-p-6">
        <!-- Header Section -->
        <div class="tw-w-full tw-flex tw-justify-between tw-items-center tw-mb-3 tw-mt-1 tw-pl-3">
            <div>
                <h3 class="tw-text-lg tw-font-semibold tw-text-slate-800">Quản lý sản phẩm</h3>
                <p class="tw-text-slate-500">Danh sách tất cả sản phẩm trong hệ thống.</p>
            </div>
            <div class="tw-mx-3">
                <div class="tw-w-full tw-max-w-sm tw-min-w-[200px] tw-relative">
                    <div class="tw-relative">
                        <input
                        class="tw-bg-white tw-w-full tw-pr-11 tw-h-10 tw-pl-3 tw-py-2 tw-bg-transparent placeholder:tw-text-slate-400 tw-text-slate-700 tw-text-sm tw-border tw-border-slate-200 tw-rounded tw-transition tw-duration-300 tw-ease focus:tw-outline-none focus:tw-border-slate-400 hover:tw-border-slate-400 tw-shadow-sm focus:tw-shadow-md"
                        placeholder="Tìm kiếm sản phẩm..."
                        id="searchInput"
                        />
                        <button
                        class="tw-absolute tw-h-8 tw-w-8 tw-right-1 tw-top-1 tw-my-auto tw-px-2 tw-flex tw-items-center tw-bg-white tw-rounded"
                        type="button"
                        >
                        <svg xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24" stroke-width="3" stroke="currentColor" class="tw-w-8 tw-h-8 tw-text-slate-600">
                            <path stroke-linecap="round" stroke-linejoin="round" d="m21 21-5.197-5.197m0 0A7.5 7.5 0 1 0 5.196 5.196a7.5 7.5 0 0 0 10.607 10.607Z" />
                        </svg>
                        </button>
                    </div>
                </div>
            </div>
        </div>

        <!-- Action Buttons -->
        <div class="tw-mb-4 tw-flex tw-flex-wrap tw-gap-3">
            <a href="/admin/san-pham/them" class="tw-bg-blue-600 hover:tw-bg-blue-700 tw-text-white tw-px-4 tw-py-2 tw-rounded-lg tw-text-sm tw-font-medium tw-transition tw-duration-200">
                Thêm sản phẩm
            </a>
            <form th:action="@{/admin/san-pham/ap-dung-giam-gia-nhieu}" method="post" class="tw-flex tw-gap-3 tw-items-center">
                <select name="dotGiamGiaId" class="tw-border tw-border-slate-300 tw-rounded-lg tw-px-3 tw-py-2 tw-text-sm focus:tw-outline-none focus:tw-border-slate-400" required>
                    <option value="">-- Chọn đợt giảm giá --</option>
                    <option th:each="dgg : ${dsDotGiamGia}" th:value="${dgg.id}" th:text="${dgg.ten}"></option>
                </select>
                <button type="submit" class="tw-bg-green-600 hover:tw-bg-green-700 tw-text-white tw-px-4 tw-py-2 tw-rounded-lg tw-text-sm tw-font-medium tw-transition tw-duration-200">
                    Áp dụng giảm giá
                </button>
            </form>
        </div>

        <!-- Alert Messages -->
        <div th:if="${error}" class="tw-bg-red-50 tw-border tw-border-red-200 tw-text-red-700 tw-px-4 tw-py-3 tw-rounded-lg tw-mb-4" role="alert">
            <div class="tw-flex tw-items-center">
                <svg class="tw-w-5 tw-h-5 tw-mr-2" fill="currentColor" viewBox="0 0 20 20">
                    <path fill-rule="evenodd" d="M18 10a8 8 0 11-16 0 8 8 0 0116 0zm-7-4a1 1 0 11-2 0 1 1 0 012 0zM9 9a1 1 0 000 2v3a1 1 0 001 1h1a1 1 0 100-2v-3a1 1 0 00-1-1H9z" clip-rule="evenodd"></path>
                </svg>
                <span th:text="${error}"></span>
            </div>
        </div>

        <div th:if="${success}" class="tw-bg-green-50 tw-border tw-border-green-200 tw-text-green-700 tw-px-4 tw-py-3 tw-rounded-lg tw-mb-4" role="alert">
            <div class="tw-flex tw-items-center">
                <svg class="tw-w-5 tw-h-5 tw-mr-2" fill="currentColor" viewBox="0 0 20 20">
                    <path fill-rule="evenodd" d="M10 18a8 8 0 100-16 8 8 0 000 16zm3.707-9.293a1 1 0 00-1.414-1.414L9 10.586 7.707 9.293a1 1 0 00-1.414 1.414l2 2a1 1 0 001.414 0l4-4z" clip-rule="evenodd"></path>
                </svg>
                <span th:text="${success}"></span>
            </div>
        </div>
        <!-- Product Table -->
        <div class="tw-relative tw-flex tw-flex-col tw-w-full tw-h-full tw-text-gray-700 tw-bg-white tw-shadow-md tw-rounded-lg tw-bg-clip-border">
            <table class="tw-w-full tw-text-left tw-table-auto" id="productTable">
                <thead>
                    <tr class="tw-border-b tw-border-slate-300 tw-bg-slate-50">
                        <th class="tw-p-3 tw-text-sm tw-font-normal tw-leading-none tw-text-slate-500 tw-w-20">Ảnh</th>
                        <th class="tw-p-3 tw-text-sm tw-font-normal tw-leading-none tw-text-slate-500 tw-w-32">Mã & Tên SP</th>
                        <th class="tw-p-3 tw-text-sm tw-font-normal tw-leading-none tw-text-slate-500 tw-w-40">Thông tin sản phẩm</th>
                        <th class="tw-p-3 tw-text-sm tw-font-normal tw-leading-none tw-text-slate-500 tw-w-40">Thông tin khác</th>
                        <th class="tw-p-3 tw-text-sm tw-font-normal tw-leading-none tw-text-slate-500 tw-w-24">Ngày tạo</th>
                        <th class="tw-p-3 tw-text-sm tw-font-normal tw-leading-none tw-text-slate-500 tw-w-32">Thao tác</th>
                    </tr>
                </thead>
                <tbody>
                    <tr th:each="sp : ${list}" class="hover:tw-bg-slate-50 tw-transition-colors">
                        <!-- Hình ảnh -->
                        <td class="tw-p-3 tw-border-b tw-border-slate-200">
                            <img th:if="${sp.hinhAnhs != null and !sp.hinhAnhs.empty}"
                                 th:src="${sp.hinhAnhs.iterator().next().url}"
                                 th:alt="${sp.ten}"
                                 class="tw-w-12 tw-h-12 tw-object-cover tw-rounded-lg tw-shadow-sm" />
                            <div th:unless="${sp.hinhAnhs != null and !sp.hinhAnhs.empty}"
                                 class="tw-w-12 tw-h-12 tw-bg-gray-100 tw-rounded-lg tw-flex tw-items-center tw-justify-center">
                                <svg class="tw-w-6 tw-h-6 tw-text-gray-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M4 16l4.586-4.586a2 2 0 012.828 0L16 16m-2-2l1.586-1.586a2 2 0 012.828 0L20 14m-6-6h.01M6 20h12a2 2 0 002-2V6a2 2 0 00-2-2H6a2 2 0 00-2 2v12a2 2 0 002 2z"></path>
                                </svg>
                            </div>
                        </td>

                        <!-- Mã & Tên sản phẩm -->
                        <td class="tw-p-3 tw-border-b tw-border-slate-200">
                            <div class="tw-space-y-1">
                                <p class="tw-font-semibold tw-text-sm tw-text-slate-800" th:text="${sp.ma}"></p>
                                <p class="tw-text-xs tw-text-slate-600 tw-font-medium" th:text="${sp.ten}"></p>
                                <p th:if="${sp.moTa != null and !sp.moTa.isEmpty()}"
                                   class="tw-text-xs tw-text-slate-400 tw-truncate tw-max-w-32"
                                   th:text="${sp.moTa}"
                                   th:title="${sp.moTa}"></p>
                            </div>
                        </td>

                        <!-- Thông tin sản phẩm (Danh mục, Loại thú, Chất liệu) -->
                        <td class="tw-p-3 tw-border-b tw-border-slate-200">
                            <div class="tw-space-y-2">
                                <div class="tw-flex tw-flex-wrap tw-gap-1">
                                    <span class="tw-inline-flex tw-items-center tw-px-2 tw-py-1 tw-rounded-full tw-text-xs tw-font-medium tw-bg-blue-100 tw-text-blue-800" th:text="${sp.danhMuc.ten}"></span>
                                    <span class="tw-inline-flex tw-items-center tw-px-2 tw-py-1 tw-rounded-full tw-text-xs tw-font-medium tw-bg-green-100 tw-text-green-800" th:text="${sp.loaiThu.ten}"></span>
                                </div>
                                <div class="tw-text-xs tw-text-slate-500">
                                    <span class="tw-font-medium">Chất liệu:</span> <span th:text="${sp.chatLieu.ten}"></span>
                                </div>
                            </div>
                        </td>

                        <!-- Thông tin khác (Thương hiệu, Xuất xứ, Kiểu dáng) -->
                        <td class="tw-p-3 tw-border-b tw-border-slate-200">
                            <div class="tw-space-y-1 tw-text-xs tw-text-slate-500">
                                <div><span class="tw-font-medium">TH:</span> <span th:text="${sp.thuongHieu.ten}"></span></div>
                                <div><span class="tw-font-medium">XX:</span> <span th:text="${sp.xuatXu.ten}"></span></div>
                                <div><span class="tw-font-medium">KD:</span> <span th:text="${sp.kieuDang.ten}"></span></div>
                            </div>
                        </td>

                        <!-- Ngày tạo -->
                        <td class="tw-p-3 tw-border-b tw-border-slate-200">
                            <p class="tw-text-xs tw-text-slate-500" th:text="${#temporals.format(sp.ngayTao, 'dd/MM/yy')}"></p>
                        </td>

                        <!-- Thao tác -->
                        <td class="tw-p-3 tw-border-b tw-border-slate-200">
                            <div class="tw-flex tw-flex-col tw-gap-1">
                                <a th:href="@{'/admin/san-pham/xem/' + ${sp.id}}"
                                   class="tw-inline-flex tw-items-center tw-justify-center tw-px-2 tw-py-1 tw-text-xs tw-font-medium tw-text-blue-600 tw-bg-blue-50 tw-rounded hover:tw-bg-blue-100 tw-transition-colors">
                                    Xem
                                </a>
                                <a th:href="@{'/admin/san-pham/sua/' + ${sp.id}}"
                                   class="tw-inline-flex tw-items-center tw-justify-center tw-px-2 tw-py-1 tw-text-xs tw-font-medium tw-text-yellow-600 tw-bg-yellow-50 tw-rounded hover:tw-bg-yellow-100 tw-transition-colors">
                                    Sửa
                                </a>
                                <a th:href="@{'/admin/san-pham/xoa/' + ${sp.id}}"
                                   class="tw-inline-flex tw-items-center tw-justify-center tw-px-2 tw-py-1 tw-text-xs tw-font-medium tw-text-red-600 tw-bg-red-50 tw-rounded hover:tw-bg-red-100 tw-transition-colors"
                                   onclick="return confirm('Bạn có chắc muốn xoá sản phẩm này?')">
                                    Xoá
                                </a>
                            </div>
                        </td>
                    </tr>
                </tbody>
            </table>
        </div>
    </div>

    <script>
        // Search functionality
        document.getElementById('searchInput').addEventListener('input', function() {
            const searchTerm = this.value.toLowerCase();
            const tableRows = document.querySelectorAll('#productTable tbody tr');

            tableRows.forEach(row => {
                const cells = row.querySelectorAll('td');
                let found = false;

                // Search in product code, name, description, category, brand, etc.
                for (let i = 1; i < cells.length - 1; i++) { // Skip image column (0) and action column (last)
                    const cellText = cells[i].textContent.toLowerCase();
                    if (cellText.includes(searchTerm)) {
                        found = true;
                        break;
                    }
                }

                row.style.display = found ? '' : 'none';
            });
        });

        // Add some hover effects and animations
        document.addEventListener('DOMContentLoaded', function() {
            const rows = document.querySelectorAll('#productTable tbody tr');
            rows.forEach(row => {
                row.addEventListener('mouseenter', function() {
                    this.style.transform = 'scale(1.01)';
                    this.style.transition = 'transform 0.2s ease';
                });

                row.addEventListener('mouseleave', function() {
                    this.style.transform = 'scale(1)';
                });
            });

            // Fix submenu display issue
            setTimeout(function() {
                console.log('Fixing submenu display...');
                const submenu = document.getElementById('ui-basic');
                const currentPath = window.location.pathname;

                if (currentPath.includes('/admin/san-pham')) {
                    if (submenu) {
                        submenu.classList.add('show');
                        const parentNavItem = submenu.closest('.nav-item');
                        if (parentNavItem) {
                            parentNavItem.classList.add('active');
                        }
                        console.log('Submenu activated for product page');
                    }
                }
            }, 500);
        });
    </script>
</section>
</body>
</html>