<!DOCTYPE html>
<!--Cấu hình layout cho Thymleaf-->
<html xmlns:layout="http://www.ultraq.net.nz/thymeleaf/layout">

<head>
    <meta charset="utf-8">
    <meta name="viewport" content="width=device-width, initial-scale=1, shrink-to-fit=no">
    <title>Star Admin</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.6/dist/css/bootstrap.min.css" rel="stylesheet"
          integrity="sha384-4Q6Gf2aSP4eDXB8Miphtr37CMZZQ5oXLH2yaXMJ2w8e2ZtHTl7GptT4jmndRuHDT" crossorigin="anonymous">
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.6/dist/js/bootstrap.bundle.min.js"
            integrity="sha384-j1CDi7MgGQ12Z7Qab0qlWQ/Qqz24Gc6BM0thvEMVjHnfYGF0rmFCozFSxQBxwHKO"
            crossorigin="anonymous"></script>
    <link rel="stylesheet" href="https://cdn.jsdelivr.net/npm/bootstrap-icons@1.11.1/font/bootstrap-icons.css">
    <link rel="stylesheet" th:href="@{/vendors/iconfonts/mdi/css/materialdesignicons.min.css}">
    <!--    <link rel="stylesheet" th:href="@{/vendors/css/vendor.bundle.base.css}">-->
    <!--    <link rel="stylesheet" th:href="@{/vendors/css/vendor.bundle.addons.css}">-->
    <link rel="stylesheet" th:href="@{/css/style.css}">
    <link rel="preconnect" href="https://fonts.googleapis.com">
    <link rel="preconnect" href="https://fonts.gstatic.com" crossorigin>
    <link href="https://fonts.googleapis.com/css2?family=Bai+Jamjuree:wght@300;400;500;600;700&display=swap" rel="stylesheet">
    <link rel="stylesheet" th:href="@{/css/font-override.css}">
    <link rel="shortcut icon" th:href="@{/images/favicon.png}"/>
    <!-- Font Awesome CDN -->
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.5.0/css/all.min.css">

    <style>
        /* Icon Animation for Main Menu and Submenu */
        .sidebar .nav-item .nav-link .menu-icon {
            transition: all 0.3s ease;
        }

        .sidebar .nav-item .nav-link:hover .menu-icon {
            transform: scale(1.2) rotate(10deg);
        }

        /* Ẩn nút tăng giảm trên Chrome, Edge, Safari */
        input[type=number]::-webkit-inner-spin-button,
        input[type=number]::-webkit-outer-spin-button {
            -webkit-appearance: none;
            margin: 0;
        }

        /* Submenu Icon Animation */
        .sidebar .sub-menu .nav-item .nav-link .menu-icon {
            transition: all 0.3s ease;
            font-size: 0.9rem;
            margin-right: 8px;
        }

        .sidebar .sub-menu .nav-item .nav-link:hover .menu-icon {
            transform: scale(1.3) rotate(15deg);
        }

        /*CSS Thong bao*/
        .custom-toast {
            position: fixed;
            top: 20px;
            right: 20px;
            padding: 12px 40px 12px 18px; /* hoặc: padding: 12px 18px; */
            border-radius: 6px;
            color: white;
            z-index: 9999;
            box-shadow: 0 2px 6px rgba(0, 0, 0, 0.2);
            min-width: 280px;
            animation: fadeIn 0.3s ease-in-out;
            overflow: hidden;
        }


        .custom-toast.success {
            background-color: #28a745;
        }

        .custom-toast.error {
            background-color: #dc3545;
        }

        .custom-toast .close-btn {
            position: absolute;
            top: 6px;
            right: 8px;
            background: transparent;
            border: none;
            color: white;
            font-size: 1.2rem;
            font-weight: bold;
            line-height: 1;
            cursor: pointer;
            padding: 0;
        }

        .custom-toast .progress-bar {
            position: absolute;
            bottom: 0;
            left: 0;
            height: 4px;
            background-color: rgba(255, 255, 255, 0.7);
            animation: progressBar 4s linear forwards;
        }

        @keyframes fadeIn {
            from {
                opacity: 0;
                transform: translateY(-20px);
            }
            to {
                opacity: 1;
                transform: translateY(0);
            }
        }

        @keyframes progressBar {
            from {
                width: 100%;
            }
            to {
                width: 0%;
            }
        }

        /*CSS Thong bao*/

        /* Force submenu display fix */
        .sidebar .nav .nav-item .collapse.show {
            display: block !important;
        }

        .sidebar .nav .nav-item.active > .nav-link {
            color: #dead6f !important;
        }
    </style>
</head>

<body>
<div class="container-scroller">

    <!-- Success Toast -->
    <div id="customSuccess" class="custom-toast success" style="display:none;">
        <i class="bi bi-check-circle-fill me-2"></i>
        <span id="successMsg"></span>
        <button class="close-btn" onclick="closeToast('customSuccess')">&times;</button>
        <div class="progress-bar"></div>
    </div>

    <!-- Error Toast -->
    <div id="customError" class="custom-toast error" style="display:none;">
        <i class="bi bi-x-circle-fill me-2"></i>
        <span id="errorMsg"></span>
        <button class="close-btn" onclick="closeToast('customError')">&times;</button>
        <div class="progress-bar"></div>
    </div>

    <!-- partial:partials/_navbar.html -->
    <nav class="navbar default-layout col-lg-12 col-12 p-0 fixed-top d-flex flex-row">
        <div class="text-center navbar-brand-wrapper d-flex align-items-top justify-content-center">
            <a class="navbar-brand brand-logo" th:href="@{/admin/thong-ke}">
                <img th:src="@{/images/logo-h.png}" alt="logo" style="width: 14rem; height: auto; padding: 0.2rem 0"/>
            </a>
            <a class="navbar-brand brand-logo-mini" th:href="@{/admin/thong-ke}">
                <img th:src="@{/images/logo-mini.svg}" alt="logo"/>
            </a>
        </div>
        <div class="navbar-menu-wrapper d-flex align-items-center">
            <button class="navbar-toggler navbar-toggler d-none d-lg-block navbar-dark align-self-center mr-3"
                    type="button" data-toggle="minimize">
                <span class="navbar-toggler-icon"></span>
            </button>
            <ul class="navbar-nav navbar-nav-right">
                <li class="nav-item dropdown d-none d-xl-inline-block">

                    <a class="nav-link dropdown-toggle" id="UserDropdown" href="#" data-toggle="dropdown" aria-expanded="false">
                        <span class="profile-text" th:if="${@get_nhan_vien.getCurrentNhanVien() != null}">
                            Xin chào, <span th:text="${@get_nhan_vien.getCurrentNhanVien().ten}"></span>!
                        </span>
                        <span class="profile-text" th:if="${@get_nhan_vien.getCurrentNhanVien() == null}">
                            Xin chào, Khách!
                        </span>
                        <img class="img-xs rounded-circle"
                             th:if="${@get_nhan_vien.getCurrentNhanVien() != null && @get_nhan_vien.getCurrentNhanVien().anh != null && !@get_nhan_vien.getCurrentNhanVien().anh.isEmpty()}"
                             th:src="@{'/uploads/' + ${@get_nhan_vien.getCurrentNhanVien().anh}}"
                             alt="Ảnh đại diện">
                        <img class="img-xs rounded-circle"
                             th:if="${@get_nhan_vien.getCurrentNhanVien() == null || @get_nhan_vien.getCurrentNhanVien().anh == null || @get_nhan_vien.getCurrentNhanVien().anh.isEmpty()}"
                             th:src="@{/images/faces-clipart/pic-1.png}"
                             alt="Ảnh mặc định">
                    </a>

                    <div class="dropdown-menu dropdown-menu-right navbar-dropdown" aria-labelledby="UserDropdown">
                        <a class="dropdown-item d-flex align-items-center gap-2 py-3 px-4" th:href="@{/admin/dang-xuat}" style="color: #e74c3c; font-weight: 500; border-radius: 8px; transition: background 0.2s;">
                            <i class="fas fa-sign-out-alt" style="font-size: 1.2rem;"></i>
                            <span>Sign Out</span>
                        </a>
                    </div>
                </li>
            </ul>
            <button class="navbar-toggler navbar-toggler-right d-lg-none align-self-center" type="button"
                    data-toggle="offcanvas">
                <span class="icon-menu"></span>
            </button>
        </div>
    </nav>

    <!-- partial -->
    <div class="container-fluid page-body-wrapper">
        <!-- partial:partials/_sidebar.html -->
        <nav class="sidebar sidebar-offcanvas" id="sidebar">
            <ul class="nav">
                <li class="nav-item nav-profile">
                    <div class="nav-link">
                        <div class="user-wrapper">
                            <div class="profile-image">
                                <img th:if="${@get_nhan_vien.getCurrentNhanVien() != null && @get_nhan_vien.getCurrentNhanVien().anh != null && !@get_nhan_vien.getCurrentNhanVien().anh.isEmpty()}"
                                     th:src="@{'/uploads/' + ${@get_nhan_vien.getCurrentNhanVien().anh}}"
                                     alt="profile image">
                                <img th:if="${@get_nhan_vien.getCurrentNhanVien() == null || @get_nhan_vien.getCurrentNhanVien().anh == null || @get_nhan_vien.getCurrentNhanVien().anh.isEmpty()}"
                                     th:src="@{/images/faces-clipart/pic-1.png}"
                                     alt="profile image">
                            </div>
                            <div class="text-wrapper">
                                <p class="profile-name"
                                   th:text="${@get_nhan_vien.getCurrentNhanVien() != null ? @get_nhan_vien.getCurrentNhanVien().ten : 'Khách'}">
                                    Tên người dùng</p>
                                <div>
                                    <small class="designation text-muted"
                                           th:text="${@get_nhan_vien.getCurrentNhanVien() != null ? @get_nhan_vien.getCurrentNhanVien().chucVu : 'Khách'}">Chức
                                        vụ</small>
                                    <span class="status-indicator online"></span>
                                </div>
                            </div>
                        </div>
<!--                        <button class="btn btn-success btn-block">New Project-->
<!--                            <i class="mdi mdi-plus"></i>-->
<!--                        </button>-->
                    </div>
                </li>
                <li class="nav-item">
                    <a class="nav-link" th:href="@{/admin/thong-ke}">
                        <i class="menu-icon fas fa-chart-line"></i>
                        <span class="menu-title">Tổng quan</span>
                    </a>
                </li>
                <li class="nav-item">
                    <a class="nav-link" th:href="@{/admin/ban-hang}">
                        <i class="menu-icon fas fa-cash-register"></i>
                        <span class="menu-title">Bán hàng</span>
                    </a>
                </li>
                <li class="nav-item">
                    <a class="nav-link" th:href="@{/admin/hoa-don}">
                        <i class="menu-icon fas fa-receipt"></i>
                        <span class="menu-title">Hoá đơn</span>
                    </a>
                </li>
                <li class="nav-item">
                    <a class="nav-link" th:href="@{/admin/don-hang}">
                        <i class="menu-icon fas fa-shopping-cart"></i>
                        <span class="menu-title">Đơn hàng</span>
                    </a>
                </li>
                <li class="nav-item">
<!--                    <a class="nav-link" data-toggle="collapse" href="#ui-basic" aria-expanded="false" aria-controls="ui-basic">-->
<!--                        <i class="menu-icon fas fa-box"></i>-->
                    <a class="nav-link" data-toggle="collapse" href="#ui-basic" aria-expanded="false"
                       aria-controls="ui-basic">
                        <i class="menu-icon mdi mdi-content-copy"></i>
                        <span class="menu-title">Sản phẩm</span>
                        <i class="menu-arrow fas fa-chevron-down"></i>
                    </a>
                    <div class="collapse" id="ui-basic">
                        <ul class="nav flex-column sub-menu">
                            <li class="nav-item">
                                <a class="nav-link" th:href="@{/admin/san-pham/hien_thi}">
                                    <i class="menu-icon fas fa-tshirt"></i>
                                    Sản phẩm
                                </a>
                            </li>
                            <li class="nav-item">
                                <a class="nav-link" th:href="@{/admin/thuong-hieu}">
                                    <i class="menu-icon fas fa-award"></i>
                                    Thương hiệu
                                </a>
                            </li>
                            <li class="nav-item">
                                <a class="nav-link" th:href="@{/admin/kieu-dang}">
                                    <i class="menu-icon fas fa-cut"></i>
                                    Kiểu dáng
                                </a>
                            </li>
                            <li class="nav-item">
                                <a class="nav-link" th:href="@{/admin/xuat-xu}">
                                    <i class="menu-icon fas fa-globe"></i>
                                    Xuất xứ
                                </a>
                            </li>
                            <li class="nav-item">
                                <a class="nav-link" th:href="@{/admin/chat-lieu}">
                                    <i class="menu-icon fas fa-leaf"></i>
                                    Chất liệu
                                </a>
                            </li>
                            <li class="nav-item">
                                <a class="nav-link" th:href="@{/admin/danh-muc}">
                                    <i class="menu-icon fas fa-list"></i>
                                    Danh mục
                                </a>
                            </li>
                            <li class="nav-item">
                                <a class="nav-link" th:href="@{/admin/loai-thu}">
                                    <i class="menu-icon fas fa-paw"></i>
                                    Loại thú
                                </a>
                            </li>
                            <li class="nav-item">
                                <a class="nav-link" th:href="@{/admin/mau-sac}">
                                    <i class="menu-icon fas fa-palette"></i>
                                    Màu sắc
                                </a>
                            </li>
                            <li class="nav-item">
                                <a class="nav-link" th:href="@{/admin/size}">
                                    <i class="menu-icon fas fa-ruler"></i>
                                    Size
                                </a>
                            </li>
                        </ul>
                    </div>
                </li>
                <li class="nav-item">
                    <a class="nav-link" th:href="@{/admin/dot-giam-gia}">
                        <i class="menu-icon fas fa-percentage"></i>
                        <span class="menu-title">Đợt giảm giá</span>
                    </a>
                </li>
                <li class="nav-item">
                    <a class="nav-link" th:href="@{/admin/phieu-giam-gia}">
                        <i class="menu-icon fas fa-ticket-alt"></i>
                        <span class="menu-title">Phiếu giảm giá</span>
                    </a>
                </li>
                <li class="nav-item">
                    <a class="nav-link" th:href="@{/admin/quanlytaikhoan/nhanvien}">
                        <i class="menu-icon fas fa-users-cog"></i>
                        <span class="menu-title">Nhân viên</span>
                    </a>
                </li>
                <li class="nav-item">
                    <a class="nav-link" th:href="@{/admin/quanlytaikhoan/khachhang}">
                        <i class="menu-icon fas fa-users"></i>
                        <span class="menu-title">Khách hàng</span>
                    </a>
                </li>
            </ul>
        </nav>

        <!-- partial -->
        <div class="main-panel">
            <!--            Các màn sẽ được đặt ở đây-->
            <div class="content-wrapper" layout:fragment="content">
                <!-- Nội dung từ các trang con sẽ được nhúng vào đây -->
            </div>
            <!-- content-wrapper ends -->

            <!-- partial:partials/_footer.html -->
            <footer class="footer">
                <div class="container-fluid clearfix">
            <span class="text-muted d-block text-center text-sm-left d-sm-inline-block">Copyright © 2025
              <span>Bootstrapdash</span>. All rights reserved.</span>
                    <span class="float-none float-sm-right d-block mt-1 mt-sm-0 text-center">Hand-crafted & made with
              <i class="mdi mdi-heart text-danger"></i>
            </span>
                </div>
            </footer>
            <!-- partial -->
        </div>
        <!-- main-panel ends -->

    </div>

    <!-- page-body-wrapper ends -->
</div>
<!-- container-scroller -->
<script src="https://code.jquery.com/jquery-3.7.1.min.js"></script>
<!-- plugins:js -->
<script th:src="@{/vendors/js/vendor.bundle.base.js}"></script>
<script th:src="@{/vendors/js/vendor.bundle.addons.js}"></script>
<script th:src="@{/js/off-canvas.js}"></script>
<script th:src="@{/js/misc.js}"></script>
<script th:src="@{/js/dashboard.js}"></script>
<script type="text/javascript">

    $(document).ready(function () {

        funApplicationDisabled();

        funContactDisabled();

        funProductDisabled();

    });

    function funApplicationDisabled() {
        $('#app-input-service-id').prop('readOnly', true);
        $('#app-input-name').prop('disabled', true);
        $('#app-input-icon').prop('disabled', true);
        $('#app-input-meta-desc').prop('disabled', true);
        $('#app-input-meta-key').prop('disabled', true);
        $('#app-input-meta-google-site').prop('disabled', true);
        $('#app-btn-cancel').prop('disabled', true);
        $('#app-btn-save').prop('disabled', true);
        $('#app-btn-edit').prop('disabled', false);
    }

    function funContactDisabled() {
        $('#contact-input-service-id').prop('readOnly', true);
        $('#contact-name').prop('disabled', true);
        $('#contact-phone').prop('disabled', true);
        $('#contact-sms').prop('disabled', true);
        $('#contact-hiMessage').prop('disabled', true);
        $('#contact-address').prop('disabled', true);
        $('#contact-email').prop('disabled', true);
        $('#contact-btn-cancel').prop('disabled', true);
        $('#contact-btn-save').prop('disabled', true);
        $('#contact-btn-edit').prop('disabled', false);
    }

    function funProductDisabled() {
        $('#product-input-uuid').prop('readOnly', true);
        $('#product-input-name').prop('disabled', true);
        $('#product-input-img-src').prop('disabled', true);
        $('#product-input-img-alt').prop('disabled', true);
        $('#product-input-href').prop('disabled', true);
        $('#product-input-price').prop('disabled', true);
        $('#product-input-brochure').prop('disabled', true);
        $('#product-btn-cancel').prop('disabled', true);
        $('#product-btn-save').prop('disabled', true);
        $('#product-btn-edit').prop('disabled', false);
    }

</script>

<!--  Thong bao-->
<script th:inline="javascript">
    /*<![CDATA[*/
    const successMsg = [[${success} == null ? 'null' : '' + ${success} + '']];
    const errorMsg = [[${error} == null ? 'null' : '' + ${error} + '']];

    function showCustomToast(type, message) {
        const toastId = type === 'success' ? 'customSuccess' : 'customError';
        const toastEl = document.getElementById(toastId);
        const msgEl = document.getElementById(type === 'success' ? 'successMsg' : 'errorMsg');
        const progressBar = toastEl.querySelector('.progress-bar');

        msgEl.textContent = message;
        toastEl.style.display = 'block';

        // Khởi động lại animation nếu toast được hiển thị lại
        progressBar.style.animation = 'none';
        void progressBar.offsetWidth; // force reflow
        progressBar.style.animation = null;

        // Auto close sau 4s
        setTimeout(() => {
            toastEl.style.display = 'none';
        }, 4000);
    }

    function closeToast(toastId) {
        const toastEl = document.getElementById(toastId);
        toastEl.style.display = 'none';
    }

    document.addEventListener("DOMContentLoaded", function () {
        if (successMsg && successMsg !== "null") showCustomToast('success', successMsg);
        if (errorMsg && errorMsg !== "null") showCustomToast('error', errorMsg);
    });
    /*]]>*/
</script>
<!--  Thong bao-->

<!-- Fix submenu display for product pages -->
<script>
$(document).ready(function() {
    // Additional fix for submenu display
    var currentPath = window.location.pathname;
    console.log('Layout script - Current path:', currentPath);

    // Force show product submenu if on product-related pages
    if (currentPath.includes('/admin/san-pham') ||
        currentPath.includes('/admin/thuong-hieu') ||
        currentPath.includes('/admin/kieu-dang') ||
        currentPath.includes('/admin/xuat-xu') ||
        currentPath.includes('/admin/chat-lieu') ||
        currentPath.includes('/admin/danh-muc') ||
        currentPath.includes('/admin/loai-thu') ||
        currentPath.includes('/admin/mau-sac') ||
        currentPath.includes('/admin/size')) {

        console.log('Product-related page detected, forcing submenu show');

        setTimeout(function() {
            $('#ui-basic').addClass('show');
            $('#ui-basic').closest('.nav-item').addClass('active');

            // Also activate the specific menu item
            $('a[href="' + currentPath + '"]').addClass('active').closest('.nav-item').addClass('active');

            console.log('Submenu forced to show');
        }, 100);
    }
});
</script>

</body>
</html>